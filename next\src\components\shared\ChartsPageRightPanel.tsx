import { BsTools } from "react-icons/bs";
import { <PERSON><PERSON>lar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LuCalendarFold } from "react-icons/lu";
import { PiChatsDuotone } from "react-icons/pi";
import { MdOutlineNotificationsActive } from "react-icons/md";
import { CgCommunity } from "react-icons/cg";
import { AiOutlineProduct } from "react-icons/ai";

const BookOpenTextIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="lucide lucide-book-open-text-icon lucide-book-open-text"
  >
    <path d="M12 7v14" />
    <path d="M16 12h2" />
    <path d="M16 8h2" />
    <path d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z" />
    <path d="M6 12h2" />
    <path d="M6 8h2" />
  </svg>
);

const ChartsPageRightPanel = () => {
  return (
    <aside className="w-10 border-l border-border bg-background">
      {/* Right panel with tools icon at the top */}
      <div className="flex h-full flex-col items-center justify-start gap-3 py-2">
        <div className="flex h-10 items-center justify-center">
          <BsTools className="h-5 w-5 cursor-pointer text-foreground hover:text-foreground/80" />
        </div>
        <div className="flex h-10 items-center justify-center">
          <LuAlarmClockCheck className="h-6 w-6 cursor-pointer text-foreground hover:text-foreground/80" />
        </div>
        <div className="flex h-10 items-center justify-center">
          <PiChatsDuotone className="h-6 w-6 cursor-pointer text-foreground hover:text-foreground/80" />
        </div>
        <div className="flex-1"></div>
        <div className="flex h-10 items-center justify-center">
          <div className="cursor-pointer text-foreground hover:text-foreground/80">
            <BookOpenTextIcon />
          </div>
        </div>
        <div className="flex h-10 items-center justify-center">
          <LuCalendarFold className="h-6 w-6 cursor-pointer text-foreground hover:text-foreground/80" />
        </div>
        <div className="flex h-10 items-center justify-center">
          <AiOutlineProduct className="h-6 w-6 cursor-pointer text-foreground hover:text-foreground/80" />
        </div>
        <div className="flex h-10 items-center justify-center">
          <CgCommunity className="h-6 w-6 cursor-pointer text-foreground hover:text-foreground/80" />
        </div>
        <div className="flex h-10 items-center justify-center">
          <MdOutlineNotificationsActive className="h-6 w-6 cursor-pointer text-foreground hover:text-foreground/80" />
        </div>
      </div>
    </aside>
  );
};

export default ChartsPageRightPanel;
