"use client";

import { useState, useEffect } from "react";

const ChartsPageFooter = () => {
  const [currentTime, setCurrentTime] = useState<string>("");

  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      // Convert to IST (GMT+5:30)
      const istTime = new Date(now.getTime() + 5.5 * 60 * 60 * 1000);
      const timeString = istTime.toISOString().substr(11, 8); // Extract HH:MM:SS
      setCurrentTime(timeString);
    };

    // Update immediately
    updateTime();

    // Update every second
    const interval = setInterval(updateTime, 1000);

    // Cleanup interval on component unmount
    return () => clearInterval(interval);
  }, []);

  return (
    <footer className="w-full border-t border-border bg-background">
      <div className="flex h-10 items-center justify-between px-4">
        <div className="flex items-center gap-8">
          <span className="text-base">Broken</span>
          <span className="text-base">Code Editor</span>
          <span className="text-base">Strategy Tester</span>
          <span className="text-base">Replay Trading</span>
          <span className="text-base">Developers</span>
        </div>
        <div className="flex items-center">
          <span className="font-mono text-base text-muted-foreground">
            IST: {currentTime}
          </span>
        </div>
      </div>
    </footer>
  );
};

export default ChartsPageFooter;
