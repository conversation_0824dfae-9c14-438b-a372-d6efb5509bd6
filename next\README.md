<div align='center'>

# [ViewMarket Clone Landing Page](https://supabase-clone-landingpage.vercel.app)

</div>

![ViewMarket Clone](https://imgur.com/OXfTkiR.png)

This project is a clone of the ViewMarket landing page, built with modern web technologies. It showcases a responsive and visually appealing design, demonstrating proficiency in front-end development and UI/UX principles.

# Features ✨

- **Responsive Design:** Ensures a seamless experience across various devices and screen sizes.
- **Modern UI Components:** Utilizes Radix UI and shadcn for sleek, accessible UI elements.
- **Type-Safe Development:** Leverages TypeScript for enhanced code quality and developer experience.
- **Customizable Styling:** Employs Tailwind CSS for flexible and efficient styling.
- **Code Quality Tools:** Integrates ESLint and Prettier for consistent code formatting and quality.
- **Optimized Performance:** Built on Next.js 13 for optimal loading speeds and SEO benefits.

## Getting Started 🚀

**Important:** This project uses npm as the package manager. Do not use pnpm.

### Installation

```bash
# Install dependencies
npm install

# Run the development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start
```

## Tech Stack 💻

**This ViewMarket clone landing page is built using the following technologies:**

- **Next.js 13:** A React framework for building efficient and scalable web applications.
- **TypeScript:** Adds static typing to JavaScript, enhancing code quality and developer productivity.
- **Radix UI:** Provides a set of accessible and customizable UI primitives.
- **shadcn:** A collection of re-usable components built with Radix UI and Tailwind CSS.
- **Tailwind CSS:** A utility-first CSS framework for rapid UI development.
- **ESLint:** A tool for identifying and reporting on patterns in JavaScript/TypeScript.
- **Prettier:** An opinionated code formatter ensuring consistent code style.
- **clsx:** A tiny utility for constructing className strings conditionally.
- **tailwind-merge:** A utility function to efficiently merge Tailwind CSS classes.

<div align='center'>

### Click [here](https://supabase-clone-landingpage.vercel.app) to visit the live demo website.

</div>
#   n e x t 
 
 
