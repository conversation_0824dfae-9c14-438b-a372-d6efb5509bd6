"use client";

import { useEffect, useRef, useState } from "react";

const TradingViewChart = () => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<any>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const mutationObserverRef = useRef<MutationObserver | null>(null);
  const [isChartReady, setIsChartReady] = useState(false);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    let isMounted = true;

    const initChart = async () => {
      // Prevent multiple initializations
      if (!chartContainerRef.current || chartRef.current) {
        return;
      }

      try {
        // Dynamic import to avoid SSR issues
        const { createChart, ColorType, CandlestickSeries } = await import(
          "lightweight-charts"
        );

        // Check if component is still mounted
        if (!isMounted || !chartContainerRef.current) {
          return;
        }

        // Clear any existing content in the container
        chartContainerRef.current.innerHTML = "";

        // Generate sample candlestick data
        const generateCandlestickData = () => {
          const data: any[] = [];
          const date = new Date(Date.UTC(2023, 0, 1, 0, 0, 0, 0));
          const numberOfPoints = 300; // Increased from 100 to ensure more data for horizontal scrolling
          let price = 100;

          for (let i = 0; i < numberOfPoints; i++) {
            const time = (date.getTime() / 1000) as any;

            // Generate realistic OHLC data
            const open = Number(price.toFixed(2));
            const volatility = Math.random() * 8 + 2; // 2-10 range
            const change = (Math.random() - 0.5) * volatility;
            const close = Math.max(open + change, 20); // Keep price above 20

            // High and low based on open and close
            const high = Math.max(open, close) + Math.random() * 3;
            const low = Math.min(open, close) - Math.random() * 3;

            data.push({
              time,
              open: Number(open.toFixed(2)),
              high: Number(Math.max(high, open, close).toFixed(2)),
              low: Number(Math.max(low, 5).toFixed(2)), // Keep low above 5
              close: Number(close.toFixed(2)),
            });

            price = close; // Update price for next iteration
            date.setUTCDate(date.getUTCDate() + 1);
          }

          return data;
        };

        // Create chart instance with theme-aware styling
        const chart = createChart(chartContainerRef.current, {
          autoSize: true,
          layout: {
            background: { type: ColorType.Solid, color: "transparent" },
            textColor: "hsl(0, 0%, 98%)",
          },
          grid: {
            vertLines: {
              color: "hsl(0, 0%, 20%)",
            },
            horzLines: {
              color: "hsl(0, 0%, 20%)",
            },
          },
          crosshair: {
            mode: 1,
          },
          rightPriceScale: {
            borderColor: "hsl(0, 0%, 20%)",
            textColor: "hsl(0, 0%, 98%)",
          },
          timeScale: {
            borderColor: "hsl(0, 0%, 20%)",
            rightOffset: 12,
            barSpacing: 3,
            fixLeftEdge: false, // Allow horizontal scrolling to the left
            lockVisibleTimeRangeOnResize: false, // Allow free panning
            rightBarStaysOnScroll: false, // Allow horizontal movement
            borderVisible: false,
            visible: true,
            timeVisible: true,
            secondsVisible: false,
            shiftVisibleRangeOnNewBar: false, // Prevent auto-shifting
          },
          handleScroll: {
            mouseWheel: true, // Allow zoom with mouse wheel
            pressedMouseMove: true, // Enable dragging with mouse
            horzTouchDrag: true, // Enable horizontal touch drag
            vertTouchDrag: true, // Enable vertical touch drag
          },
          handleScale: {
            axisPressedMouseMove: {
              time: true, // Allow horizontal scaling on time axis
              price: true, // Allow vertical scaling on price axis
            },
            mouseWheel: true, // Allow scaling with mouse wheel
            pinch: true, // Allow pinch-to-zoom on touch devices
          },
        });

        // Comprehensive watermark and branding removal
        const removeWatermarks = () => {
          if (!chartContainerRef.current) return;

          // Target only specific TradingView watermark selectors
          const watermarkSelectors = [
            '[class*="tv-attribution"]',
            'a[href*="tradingview"]',
            'div[title*="TradingView"]',
            'div[title*="tradingview"]',
            '[data-name*="tradingview"]',
            '[data-name*="tv-attribution"]',
          ];

          watermarkSelectors.forEach((selector) => {
            const elements =
              chartContainerRef.current?.querySelectorAll(selector);
            elements?.forEach((el) => {
              const element = el as HTMLElement;
              // Only remove if it's clearly a watermark (contains specific text or is a link)
              const isWatermark =
                element.textContent?.includes("TradingView") ||
                element.textContent?.includes("Chart by") ||
                (element as HTMLAnchorElement).href?.includes("tradingview") ||
                element.title?.includes("TradingView");

              if (isWatermark) {
                element.remove();
              }
            });
          });

          // Hide any remaining watermark elements with CSS
          const style = document.createElement("style");
          style.textContent = `
            /* Targeted TradingView watermark removal */
            div[class*="tv-attribution"],
            div[class*="watermark"],
            a[href*="tradingview"],
            [data-name*="tradingview"],
            [data-name*="tv-attribution"],
            div[title*="TradingView"],
            div[title*="Chart by TradingView"] {
              display: none !important;
              visibility: hidden !important;
              opacity: 0 !important;
            }
            
            /* Hide watermark positioned at bottom corners */
            div[style*="position: absolute"][style*="bottom: 0"][style*="left: 0"],
            div[style*="position: absolute"][style*="bottom: 0"][style*="right: 0"],
            div[style*="position: absolute"][style*="bottom: 5px"],
            div[style*="position: absolute"][style*="z-index"][style*="bottom"] {
              display: none !important;
            }
          `;
          if (!document.head.querySelector("style[data-chart-cleanup]")) {
            style.setAttribute("data-chart-cleanup", "true");
            document.head.appendChild(style);
          }
        };

        // Remove watermarks immediately and with delays to catch dynamic content
        removeWatermarks();
        setTimeout(removeWatermarks, 100);
        setTimeout(removeWatermarks, 500);
        setTimeout(removeWatermarks, 1000);

        // Set up mutation observer to catch dynamically added watermarks
        const observer = new MutationObserver(() => {
          removeWatermarks();
        });

        mutationObserverRef.current = observer;

        if (chartContainerRef.current) {
          observer.observe(chartContainerRef.current, {
            childList: true,
            subtree: true,
            attributes: true,
          });
        }

        // Store chart reference
        chartRef.current = chart;

        // Add candlestick series using correct v5.x API
        const candlestickSeries = chart.addSeries(CandlestickSeries, {
          upColor: "hsl(142, 76%, 36%)", // Green for up candles
          downColor: "hsl(0, 84%, 60%)", // Red for down candles
          borderVisible: true,
          wickUpColor: "hsl(142, 76%, 36%)",
          wickDownColor: "hsl(0, 84%, 60%)",
          borderUpColor: "hsl(142, 76%, 36%)",
          borderDownColor: "hsl(0, 84%, 60%)",
        });

        // Set sample candlestick data
        const sampleData = generateCandlestickData();
        candlestickSeries.setData(sampleData);

        // Set visible range to show only a portion of data, enabling horizontal scrolling
        const totalDataPoints = sampleData.length;
        const visibleDataPoints = Math.min(50, totalDataPoints); // Show only 50 points initially
        const startIndex = Math.max(0, totalDataPoints - visibleDataPoints);

        if (sampleData.length > visibleDataPoints) {
          chart.timeScale().setVisibleRange({
            from: sampleData[startIndex].time,
            to: sampleData[totalDataPoints - 1].time,
          });
        }

        if (isMounted) {
          setIsChartReady(true);
        }

        // Use ResizeObserver for better performance than window resize events
        if (chartContainerRef.current) {
          resizeObserverRef.current = new ResizeObserver((entries) => {
            if (chartRef.current && entries.length > 0) {
              const { width, height } = entries[0].contentRect;
              chartRef.current.applyOptions({
                width: Math.floor(width),
                height: Math.floor(height),
              });
            }
          });
          resizeObserverRef.current.observe(chartContainerRef.current);
        }
      } catch (error) {
        console.error("Error initializing chart:", error);
        if (isMounted) {
          setIsChartReady(false);
        }
      }
    };

    // Initialize chart
    initChart();

    // Cleanup function
    return () => {
      isMounted = false;

      // Disconnect ResizeObserver
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
        resizeObserverRef.current = null;
      }

      // Disconnect mutation observer
      if (mutationObserverRef.current) {
        mutationObserverRef.current.disconnect();
        mutationObserverRef.current = null;
      }

      // Clean up chart
      if (chartRef.current) {
        try {
          chartRef.current.remove();
        } catch (error) {
          console.error("Error removing chart:", error);
        }
        chartRef.current = null;
      }

      // Reset state
      setIsChartReady(false);
    };
  }, []); // Empty dependency array - only run once on mount

  return (
    <div className="flex h-full w-full flex-col">
      {!isChartReady && (
        <div className="flex h-full items-center justify-center text-muted-foreground">
          Loading chart...
        </div>
      )}
      {/* eslint-disable jsx-a11y/no-static-element-interactions, jsx-a11y/click-events-have-key-events */}
      <div
        ref={chartContainerRef}
        className="w-full flex-1 bg-background"
        role="application"
        aria-label="Trading chart - interactive financial data visualization"
        tabIndex={0}
        style={{
          minHeight: "400px",
          position: "relative",
          overflow: "hidden",
          cursor: "grab", // Show grab cursor to indicate draggable
          userSelect: "none", // Prevent text selection while dragging
        }}
        onMouseDown={(e) => {
          // Change cursor to grabbing while dragging
          const target = e.target as HTMLElement;
          target.style.cursor = "grabbing";
        }}
        onMouseUp={(e) => {
          // Reset cursor after dragging
          const target = e.target as HTMLElement;
          target.style.cursor = "grab";
        }}
        onMouseLeave={(e) => {
          // Reset cursor when leaving the chart area
          const target = e.target as HTMLElement;
          target.style.cursor = "grab";
        }}
        onKeyDown={(e) => {
          // Add keyboard support for accessibility
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
          }
        }}
      />
      {/* eslint-enable jsx-a11y/no-static-element-interactions, jsx-a11y/click-events-have-key-events */}
    </div>
  );
};

export default TradingViewChart;
