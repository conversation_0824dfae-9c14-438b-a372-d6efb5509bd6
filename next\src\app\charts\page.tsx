"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import ChartsPageHeader from "@/components/shared/ChartsPageHeader";
import ChartsPageFooter from "@/components/shared/ChartsPageFooter";
import ChartsPageRightPanel from "@/components/shared/ChartsPageRightPanel";
import TradingViewChart from "@/components/shared/TradingViewChart";

const ChartsPage = () => {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth");
    }
  }, [status, router]);

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="flex min-h-screen flex-col bg-background">
      <ChartsPageHeader session={session} />
      <div className="flex flex-1">
        <main className="flex-1">
          <TradingViewChart />
        </main>
        <ChartsPageRightPanel />
      </div>
      <ChartsPageFooter />
    </div>
  );
};

export default ChartsPage;
