import { CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import GraterIcon from "@/utils/icons/GraterIcon";

const VideoSection = () => {
  return (
    <div className="flex w-full flex-col gap-5 px-6 pt-48 md:max-w-[768px] lg:max-w-[1024px] lg:px-16 xl:max-w-[1280px] xl:px-20 2xl:max-w-[1536px]">
      <h3 className=" pb-14 text-2xl ">
        Analyze and research without leaving the platform
      </h3>
      <Tabs defaultValue="Table editor" className="h-full">
        <div className="flex w-full flex-col justify-between gap-10 lg:flex-row ">
          <div className="relative aspect-video lg:w-[50%]">
            <TabsContent
              value="Table editor"
              className="relative h-full w-full  "
            >
              <iframe
                title="Demo video showcasing ViewMarket"
                className="l-0 r-0 absolute h-full w-full overflow-hidden rounded-md  border border-border bg-black "
                src="https://www.youtube.com/embed/xIHjwJgxOmk?si=OA4AnxU6hZDYQb1z&amp;autoplay=1&amp;loop=1&amp;controls=0&amp;modestbranding=1&amp;rel=0&amp;disablekb=1&amp;mute=1&amp;muted=1"
                allow="autoplay; modestbranding; encrypted-media"
                loading="lazy"
              ></iframe>
            </TabsContent>
            <TabsContent
              value="SQL Editor"
              className="relative aspect-video h-full w-full "
            >
              <iframe
                title="Demo video showcasing ViewMarket"
                className="l-0 r-0 absolute h-full w-full overflow-hidden rounded-md  border border-border bg-black "
                src="https://www.youtube.com/embed/Hch1mZPZ53A?si=LAH_BB756M1OSWpT&amp;autoplay=1&amp;loop=1&amp;controls=0&amp;modestbranding=1&amp;rel=0&amp;disablekb=1&amp;mute=1&amp;muted=1"
                allow="autoplay; modestbranding; encrypted-media"
                loading="lazy"
              ></iframe>
            </TabsContent>
            <TabsContent
              value="Auth rules"
              className="relative aspect-video h-full w-full "
            >
              <iframe
                title="Demo video showcasing ViewMarket"
                className="l-0 r-0 absolute h-full w-full overflow-hidden rounded-md  border border-border bg-black "
                src="https://www.youtube.com/embed/vP319FCIZ6Y?si=QET9fpysJRKQcmRi&amp;autoplay=1&amp;loop=1&amp;controls=0&amp;modestbranding=1&amp;rel=0&amp;disablekb=1&amp;mute=1&amp;muted=1"
                allow="autoplay; modestbranding; encrypted-media"
                loading="lazy"
              ></iframe>
            </TabsContent>
          </div>
          <div className=" flex flex-col gap-3 overflow-hidden pt-5 lg:w-[40%]">
            <div>
              <TabsList className="">
                <TabsTrigger value="Table editor" variant="underline">
                  Chart Analysis
                </TabsTrigger>
                <TabsTrigger value="SQL Editor" variant="underline">
                  AI Insights
                </TabsTrigger>
                <TabsTrigger value="Auth rules" variant="underline">
                  Research Tools
                </TabsTrigger>{" "}
              </TabsList>{" "}
              <div className=" mt-[-4px] border border-secondary"></div>
              <TabsContent value="Table editor">
                <div className="mt-10 flex flex-col gap-5 text-lg">
                  Advanced charting with the precision of professional trading
                  platforms
                  <div className=" text-base text-foreground-light">
                    You don&apos;t need to be a trading expert to use
                    ViewMarket. Our intuitive charting interface makes complex
                    market analysis accessible to everyone. Analyze stocks,
                    crypto, and global markets with ease.
                  </div>
                  <CardDescription className="flex items-center  gap-2 pb-5 text-sm text-foreground-light group-hover:text-foreground">
                    Explore Chart Features <GraterIcon />
                  </CardDescription>
                </div>
              </TabsContent>
              <TabsContent value="SQL Editor">
                <div className="mt-10 flex flex-col gap-5 text-lg">
                  AI-powered market analysis for superior research decisions
                  <div className=" text-base text-foreground-light">
                    Get instant AI insights on market trends, technical
                    patterns, and fundamental analysis. Our advanced algorithms
                    analyze thousands of data points to provide actionable
                    market research recommendations.
                  </div>
                  <CardDescription className="flex items-center  gap-2 pb-5 text-sm text-foreground-light group-hover:text-foreground">
                    Explore AI Features <GraterIcon />
                  </CardDescription>
                </div>
              </TabsContent>
              <TabsContent value="Auth rules">
                <div className="mt-10 flex flex-col gap-5 text-lg">
                  Market research tools as intuitive as they can be
                  <div className=" text-base text-foreground-light">
                    Easily conduct market research with ViewMarket&apos;s
                    comprehensive analysis toolkit. Monitor market trends,
                    analyze financial data, and track market movements across
                    multiple sectors.
                  </div>
                  <CardDescription className="flex items-center  gap-2 pb-5 text-sm text-foreground-light group-hover:text-foreground">
                    Explore Research Tools <GraterIcon />
                  </CardDescription>
                </div>
              </TabsContent>
            </div>
            <div></div>
          </div>
        </div>
      </Tabs>
    </div>
  );
};

export default VideoSection;
