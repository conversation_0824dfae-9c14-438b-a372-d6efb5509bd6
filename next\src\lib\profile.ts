import { supabase } from "./supabase";

export async function getUserProfile(email: string) {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("email", email)
    .single();

  if (error && error.code !== "PGRST116") {
    console.error("Error fetching user profile:", error);
    return null;
  }

  return data;
}

export async function createUserProfile(userData: {
  id: string;
  email: string;
  name?: string | null;
  avatar_url?: string | null;
  provider: string;
}) {
  const { data, error } = await supabase
    .from("profiles")
    .insert(userData)
    .select()
    .single();

  if (error) {
    console.error("Error creating user profile:", error);
    return null;
  }

  return data;
}

export async function updateUserProfile(
  email: string,
  updates: {
    name?: string | null;
    avatar_url?: string | null;
  },
) {
  const { data, error } = await supabase
    .from("profiles")
    .update({
      ...updates,
      updated_at: new Date().toISOString(),
    })
    .eq("email", email)
    .select()
    .single();

  if (error) {
    console.error("Error updating user profile:", error);
    return null;
  }

  return data;
}
