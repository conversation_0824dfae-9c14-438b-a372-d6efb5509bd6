# Authentication Setup Guide

## Prerequisites

1. **Supabase Project**: Create a project at [supabase.com](https://supabase.com)
2. **Google OAuth App**: Create at [Google Cloud Console](https://console.cloud.google.com)
3. **GitHub OAuth App**: Create at [GitHub Developer Settings](https://github.com/settings/developers)

## Environment Variables Setup

Update `.env.local` with your actual values:

```bash
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here

# Google OAuth (from Google Cloud Console)
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret

# GitHub OAuth (from GitHub Developer Settings)
AUTH_GITHUB_ID=your-github-client-id
AUTH_GITHUB_SECRET=your-github-client-secret

# Supabase Configuration (from Supabase Dashboard)
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
```

## OAuth Provider Setup

### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing
3. Enable Google+ API
4. Go to Credentials → Create Credentials → OAuth 2.0 Client ID
5. Set authorized redirect URI: `http://localhost:3000/api/auth/callback/google`
6. Copy Client ID and Client Secret to `.env.local`

### GitHub OAuth Setup

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Set Authorization callback URL: `http://localhost:3000/api/auth/callback/github`
4. Copy Client ID and Client Secret to `.env.local`

## Database Setup

The profiles table has been automatically created in your Supabase project with:

- User profile storage
- Row Level Security (RLS) policies
- Automatic timestamp updates

## Running the Application

```bash
npm install
npm run dev
```

## Authentication Flow

1. User visits `/auth` page
2. Clicks Google or GitHub sign-in
3. OAuth provider authenticates user
4. NextAuth creates session and stores user in Supabase profiles table
5. User is redirected to `/charts` page
6. Charts page displays user info and sign-out option

## Features Implemented

✅ Google OAuth authentication  
✅ GitHub OAuth authentication  
✅ Supabase user profile storage  
✅ Automatic user registration  
✅ Protected routes middleware  
✅ Session management  
✅ User profile display  
✅ Sign-out functionality

## Security Features

- Row Level Security (RLS) on profiles table
- Protected routes with middleware
- Secure session management
- Environment variable protection
