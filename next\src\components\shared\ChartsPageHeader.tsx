import { Search } from "lucide-react";
import { MdOutlineSettings } from "react-icons/md";
import { IoCameraReverseOutline } from "react-icons/io5";
import { BsFullscreen } from "react-icons/bs";
import { Session } from "next-auth";

import ProfileIcon from "@/utils/icons/ProfileIcon";

import CandlestickIcon from "../../utils/icons/CandlestickIcon";

import AIButton from "./AIButton";

interface ChartsPageHeaderProps {
  session: Session;
}

const ChartsPageHeader = ({ session }: ChartsPageHeaderProps) => {
  return (
    <header className="w-full border-b border-border bg-background">
      <div className="flex h-10 items-center gap-8 px-4">
        <ProfileIcon session={session} />
        <div className="flex items-center gap-1">
          <Search className="h-5 w-5" />
          <span className="text-base">Symbol</span>
        </div>
        <span className="text-base">1m</span>
        <div className="flex items-center gap-1">
          <CandlestickIcon className="h-5 w-5" />
        </div>
        <span className="text-base">Indicators</span>
        <AIButton />
        <div className="ml-auto flex items-center gap-6">
          <BsFullscreen className="h-5 w-5 cursor-pointer text-foreground hover:text-foreground/80" />
          <IoCameraReverseOutline className="h-6 w-6 cursor-pointer text-foreground hover:text-foreground/80" />
          <MdOutlineSettings className="h-5 w-5 cursor-pointer text-foreground hover:text-foreground/80" />
          <button className="rounded-full border border-border bg-white px-4 py-1 text-sm font-medium text-black transition-colors hover:bg-gray-50">
            Publish
          </button>
        </div>
      </div>
    </header>
  );
};

export default ChartsPageHeader;
