export default function LangChainIcon() {
  return (
    <svg
      width="47"
      height="46"
      viewBox="0 0 47 46"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M43.0122 14.2803C40.6216 11.8095 36.5564 12.0429 33.3052 15.6785C32.0377 17.0958 31.2424 18.3925 30.8887 19.5995C29.3516 19.9387 27.7583 20.88 26.3239 22.4844C23.072 26.1207 22.9289 28.9643 25.3677 31.5282C27.8065 34.0922 30.9621 34.0922 34.8359 30.5961C36.4314 29.1564 37.32 27.7083 37.6479 26.3303C38.9305 25.97 40.3203 25.1409 41.8172 23.7895C45.6904 20.2941 45.4035 16.751 43.0122 14.2803ZM33.2096 28.9181C30.5681 31.3026 28.7397 31.6474 27.0767 29.8986C25.4137 28.1499 25.8278 26.5034 28.0452 24.0236C28.9424 23.0199 29.8793 22.3491 30.7838 22.0169C30.9994 22.9638 31.5272 23.8596 32.349 24.7231C33.1931 25.6105 34.1227 26.1901 35.1386 26.4249C34.8086 27.2106 34.1788 28.0433 33.2096 28.9181ZM33.3009 22.0309C33.6769 22.1977 34.0299 22.4451 34.3499 22.776C34.7037 23.1419 34.974 23.5253 35.1545 23.9269C34.777 23.7439 34.4139 23.4664 34.058 23.0921C33.7201 22.7367 33.4691 22.3856 33.3009 22.0309ZM40.1909 22.1122C39.2483 22.9631 38.41 23.5526 37.6457 23.8813C37.3933 22.858 36.8297 21.9125 36.0309 21.0868C35.2803 20.3116 34.365 19.8027 33.362 19.5812C33.6827 18.8698 34.2406 18.0967 35.0265 17.2178C37.2445 14.738 39.6998 14.2858 41.3312 15.9708C42.9626 17.6558 42.8331 19.727 40.1909 22.1122ZM22.4026 30.9689C22.8332 30.7355 21.3982 29.7101 20.3463 28.6846C16.7118 22.1585 14.5125 20.1539 13.03 19.1284C11.5474 18.103 10.9737 17.3573 9.68236 15.2124C8.39183 13.0684 7.05306 13.0214 6.28808 12.9751C5.52308 12.9282 3.56242 13.1153 3.37117 14.7001C1.79301 15.6323 2.09642 17.9474 2.79742 18.4759C2.76579 18.103 2.82115 17.8311 3.02031 17.6678C3.37117 18.0715 3.88093 18.2271 5.02842 18.1338C6.08029 19.5637 5.72943 19.1593 6.36714 22.0183C7.00493 24.8773 6.84531 24.9397 9.74636 27.177C9.61911 30.2849 10.3518 29.9428 10.3518 29.9428C10.3518 29.9428 10.3518 30.2225 10.2562 30.7818C9.45949 31.3103 8.98069 31.0306 8.53489 30.9682C8.0884 30.9059 7.64266 30.751 7.19617 30.9991C7.00493 30.9991 6.30389 31.2479 6.33551 31.7448C6.39951 31.5892 6.62238 31.496 6.97325 31.4652C7.06887 31.714 7.45136 31.7448 7.45136 31.7448C7.45136 31.7448 7.19617 32.1486 7.48304 32.335C7.48304 32.1794 7.61029 32.0553 7.89716 31.9621C8.0884 32.1177 8.1524 32.2726 8.63051 32.0245C9.10862 31.7757 9.17262 31.5892 10.671 31.5276C10.0332 31.6831 10.1612 32.3974 10.1612 32.3974C10.1612 32.3974 10.4797 31.838 11.0858 32.1801C11.6919 32.5221 11.8825 31.7603 12.8869 31.9936C12.1219 32.0406 12.1219 33.0191 12.1219 33.0191C12.1219 33.0191 12.2657 32.4597 12.6956 32.8326C13.1256 33.2055 13.5562 32.4128 14.3694 32.0869C15.1826 31.7603 15.6126 32.5067 15.8038 32.0399C15.9951 31.5738 16.617 32.0869 16.617 32.0869C16.617 32.0869 16.7607 31.5276 15.7563 31.4343C14.7519 31.3411 14.5132 31.4813 14.2738 30.9213C14.9431 30.7811 14.9431 29.2433 14.9431 29.2433C14.9431 29.2433 16.76 29.6162 18.482 29.8958C19.2952 30.875 19.7733 31.5276 20.7295 31.6208C21.2077 31.9004 21.8296 32.0406 22.3551 31.9004C24.029 32.274 23.5983 31.8549 22.4026 30.9689ZM5.52308 15.3989C5.12692 15.3989 4.80554 15.0856 4.80554 14.6994C4.80554 14.3132 5.12692 13.9999 5.52308 13.9999C5.91924 13.9999 6.24062 14.3132 6.24062 14.6994C6.24062 15.0856 5.91924 15.3989 5.52308 15.3989Z"
        fill="hsl(var(--muted-foreground))"
      />
    </svg>
  );
}
