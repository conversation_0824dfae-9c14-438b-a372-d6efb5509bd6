import NextAuth from "next-auth";

import authConfig from "../../auth.config";

import { supabase } from "./supabase";

export const { handlers, auth, signIn, signOut } = NextAuth({
  ...authConfig,
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider && user.email) {
        try {
          // Check if user exists in profiles table
          const { data: existingUser } = await supabase
            .from("profiles")
            .select("*")
            .eq("email", user.email)
            .single();

          if (!existingUser) {
            // Create new user profile
            await supabase.from("profiles").insert({
              id: user.id,
              email: user.email,
              name: user.name,
              avatar_url: user.image,
              provider: account.provider,
            });
          } else {
            // Update existing user profile
            await supabase
              .from("profiles")
              .update({
                name: user.name,
                avatar_url: user.image,
                updated_at: new Date().toISOString(),
              })
              .eq("email", user.email);
          }
        } catch (error) {
          console.error("Error managing user profile:", error);
          return false;
        }
      }
      return true;
    },
    async redirect({ url, baseUrl }) {
      // Redirect to charts page after successful authentication
      if (url.startsWith("/")) return `${baseUrl}/charts`;
      else if (new URL(url).origin === baseUrl) return url;
      return `${baseUrl}/charts`;
    },
  },
  pages: {
    signIn: "/auth",
  },
});
