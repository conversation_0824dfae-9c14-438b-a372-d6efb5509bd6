{"name": "shadcn-practice", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.57.4", "@vercel/analytics": "^1.3.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "input-otp": "^1.2.4", "lightweight-charts": "^5.0.8", "lucide-react": "^0.363.0", "next": "^15.5.3", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.3.0", "react": "^18.2.0", "react-blurhash": "^0.3.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-resizable-panels": "^2.0.16", "recharts": "^2.12.7", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "youtube-player": "^5.6.0"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.51.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.16.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "^13.5.6", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^12.1.1", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.3.0", "typescript": "^5"}}