"use client";

import React from "react";

import { Button } from "@/components/ui/button";

interface TimeframeButtonProps {
  timeframe: string;
  isActive?: boolean;
  onClick?: () => void;
}

const TimeframeButton: React.FC<TimeframeButtonProps> = ({
  timeframe,
  isActive = false,
  onClick,
}) => {
  return (
    <Button
      variant={isActive ? "default" : "outline"}
      size="sm"
      onClick={onClick}
      className={`
        ${isActive ? "bg-brand-500 text-white" : "bg-background text-foreground"}
        transition-colors hover:bg-brand-500/90
      `}
    >
      {timeframe}
    </Button>
  );
};

export default TimeframeButton;
