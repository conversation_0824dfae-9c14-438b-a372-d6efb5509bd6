import Image from "next/image";
import React from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardDescription } from "@/components/ui/card";
import MessageIcon from "@/utils/icons/MessageIcon";
import TwitterIcon from "@/utils/icons/socialMedia/TwitterIcon";
import {
  person1,
  person2,
  person3,
  person4,
  person5,
  person6,
  person7,
  person8,
  person9,
} from "@/utils/images";

const personList1 = [
  {
    img: person1,
    name: "@AnalystTex",
    desc: "Using @viewmarket has revolutionized my market analysis. The AI-powered technical indicators and real-time market data give me comprehensive insights. The platform's intuitive interface makes complex market analysis accessible to analysts of all levels.",
  },
  {
    img: person2,
    name: "@MarketPro",
    desc: "@viewmarket's fundamental analysis AI is incredible! 🤯 Now I can quickly analyze financial statements, earnings reports, and market sentiment across multiple stocks simultaneously. The platform has become an essential part of my investment research workflow. #analysis #investing",
  },
];

const personList2 = [
  {
    img: person3,
    name: "@MarketAnalyst",
    desc: "I've been using @viewmarket for market research and it has been amazing being able to analyze multiple asset classes with real-time AI insights and predictive analytics.",
  },
  {
    img: person4,
    name: "@ChartExpert",
    desc: "Y'all @viewmarket + live market data is amazing! 🙌 Barely an hour into analyzing a new stock and already have comprehensive technical and fundamental insights. 🤯🤯🤯",
  },
  {
    img: person5,
    name: "@QuantAnalyst",
    desc: "And thanks to @viewmarket's AI insights, I was able to identify market opportunities and patterns in real-time. The platform's predictive modeling is absolutely amazing!",
  },
];

const personList3 = [
  {
    img: person6,
    name: "@FinTechAnalyst",
    desc: "Contributing to the financial analysis community and seeing effective strategies gives enormous satisfaction! Special thanks to @viewmarket, for providing advanced market analysis tools and staying accessible to analysts at all levels✌🏼",
  },
  {
    img: person7,
    name: "@WallStreetPro",
    desc: "Holy crap. @viewmarket is absolutely incredible. Most elegant market analysis platform I've ever used. The AI insights are a dream come true.",
  },
];

const personList4 = [
  {
    img: person8,
    name: "@HedgeFundAnalyst",
    desc: "Over the course of a few weeks, we migrated our entire research team (125+ analysts, multiple asset classes, global markets) to @viewmarket and have now completed the transition. The real-time analytics and AI insights have improved our analysis quality significantly 😅 Went smoothly, besides a few edge cases (legacy data integration)",
  },
  {
    img: person9,
    name: "@CryptoAnalyst",
    desc: "Using @viewmarket I'm really pleased with the power of AI-driven market analysis. Despite being skeptical about automated analysis platforms, I have to say the insights are incredibly accurate. The whole research experience feels very robust and comprehensive.",
  },
];

const CommunitySection: React.FC = () => {
  return (
    <div className="flex w-full flex-col gap-4 px-6 md:max-w-[768px] lg:max-w-[1024px] lg:px-16 xl:max-w-[1280px] xl:px-20 2xl:max-w-[1536px] ">
      <div className="flex flex-col gap-5 pt-20">
        <div className="flex flex-col items-center gap-4">
          <div className="text-4xl text-white">Join the community</div>
          <CardDescription className="text-base text-foreground-light">
            Supported by a network of analysts, researchers, and financial
            institutions worldwide.
          </CardDescription>

          <div className="flex gap-5">
            <Button
              variant="outline"
              className="border-border bg-card p-3"
              size="sm"
            >
              Analysis Community{" "}
              <div className="pl-2 text-foreground">
                <MessageIcon />
              </div>
            </Button>
            <Button
              variant="outline"
              className="border-border bg-card p-3"
              size="sm"
            >
              Support Forum{" "}
              <div className="pl-2 text-foreground">
                <MessageIcon />
              </div>
            </Button>
          </div>
        </div>
        <div className="relative columns-1 gap-4 overflow-hidden transition-all sm:columns-2 lg:columns-3 xl:columns-4">
          {/* <div className=" break-inside-avoid-column"> */}
          {personList1.map((data, index) => (
            <Card
              key={index}
              className="group relative mb-4 flex  break-inside-avoid-column flex-col gap-4 overflow-clip p-5"
            >
              <div className="flex items-center gap-5">
                <div className="relative">
                  <Image
                    src={data.img}
                    alt=""
                    className="z-0 h-10 w-10 rounded-full object-cover"
                  />
                  <div className="absolute left-[-16px] top-[-14px] z-10 scale-[0.6] overflow-hidden rounded-full bg-black p-2.5">
                    <TwitterIcon />
                  </div>
                </div>
                <div className="text-sm font-medium text-foreground">
                  {data.name}
                </div>
              </div>
              <CardDescription className="text-base text-[#707070]">
                &quot;{data.desc}&quot;
              </CardDescription>
            </Card>
          ))}
          {/* </div>
          <div className=" break-inside-avoid-column"> */}
          {personList2.map((data, index) => (
            <Card
              key={index}
              className="group relative mb-4 flex  break-inside-avoid-column flex-col gap-4 overflow-clip p-5"
            >
              <div className="flex items-center gap-5">
                <div className="relative">
                  <Image
                    src={data.img}
                    alt=""
                    className="z-0 h-10 w-10 rounded-full object-cover"
                  />
                  <div className="absolute left-[-16px] top-[-14px] z-10 scale-[0.6] overflow-hidden rounded-full bg-black p-2.5">
                    <TwitterIcon />
                  </div>
                </div>
                <div className="text-sm font-medium text-foreground">
                  {data.name}
                </div>
              </div>
              <CardDescription className="text-base text-[#707070]">
                &quot;{data.desc}&quot;
              </CardDescription>
            </Card>
          ))}
          {/* </div>
          <div className=" break-inside-avoid-column"> */}
          {personList3.map((data, index) => (
            <Card
              key={index}
              className="group relative mb-4 flex  break-inside-avoid-column flex-col gap-4 overflow-clip p-5"
            >
              <div className="flex items-center gap-5">
                <div className="relative">
                  <Image
                    src={data.img}
                    alt=""
                    className="z-0 h-10 w-10 rounded-full object-cover"
                  />
                  <div className="absolute left-[-16px] top-[-14px] z-10 scale-[0.6] overflow-hidden rounded-full bg-black p-2.5">
                    <TwitterIcon />
                  </div>
                </div>
                <div className="text-sm font-medium text-foreground">
                  {data.name}
                </div>
              </div>
              <CardDescription className="text-base text-[#707070]">
                &quot;{data.desc}&quot;
              </CardDescription>
            </Card>
          ))}
          {/* </div>
          <div className=" break-inside-avoid-column"> */}
          {personList4.map((data, index) => (
            <Card
              key={index}
              className="group relative mb-4 flex  break-inside-avoid-column flex-col gap-5 overflow-clip p-5"
            >
              <div className="flex items-center gap-5">
                <div className="relative">
                  <Image
                    src={data.img}
                    alt=""
                    className="z-0 h-10 w-10 rounded-full object-cover"
                  />
                  <div className="absolute left-[-16px] top-[-14px] z-10 scale-[0.6] overflow-hidden rounded-full bg-black p-2.5">
                    <TwitterIcon />
                  </div>
                </div>
                <div className="text-sm font-medium text-foreground">
                  {data.name}
                </div>
              </div>
              <CardDescription className="text-base text-[#707070]">
                &quot;{data.desc}&quot;
              </CardDescription>
            </Card>
          ))}
          {/* </div> */}
          <div className="absolute bottom-0 left-0 z-10 h-[400px] w-full bg-gradient-to-t from-background via-[#1c1c1c00]"></div>
        </div>

        <div className="flex w-full justify-center py-20">
          <Button variant="outline" className="w-fit bg-card" size="sm">
            Show More
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CommunitySection;
