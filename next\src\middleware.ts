import NextAuth from "next-auth";
import { NextResponse } from "next/server";

import authConfig from "../auth.config";

const { auth } = NextAuth({
  ...authConfig,
  secret: process.env.NEXTAUTH_SECRET,
});

export default auth((req) => {
  const { pathname } = req.nextUrl;

  // Protect charts route
  if (pathname.startsWith("/charts")) {
    if (!req.auth) {
      return NextResponse.redirect(new URL("/auth", req.url));
    }
  }

  // Redirect authenticated users away from auth page
  if (pathname === "/auth" && req.auth) {
    return NextResponse.redirect(new URL("/charts", req.url));
  }

  return NextResponse.next();
});

export const config = {
  matcher: ["/charts/:path*", "/auth"],
};
