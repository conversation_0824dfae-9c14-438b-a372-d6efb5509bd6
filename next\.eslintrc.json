// {
//   "root": true,
//   "env": {
//     "browser": true,
//     "node": true,
//     "es6": true
//   },
//   "parser": "@typescript-eslint/parser",
//   "plugins": [
//     "@typescript-eslint",
//     "prettier",
//     "simple-import-sort",
//     "import",
//     "jsx-a11y"
//   ],
//   "parserOptions": {
//     "ecmaFeatures": {
//       "jsx": true
//     },
//     "ecmaVersion": 2018,
//     "sourceType": "module"
//   },
//   "extends": [
//     "next/core-web-vitals",
//     "plugin:import/errors",
//     "plugin:import/warnings",
//     "plugin:import/typescript",
//     "plugin:react/recommended",
//     "plugin:jsx-a11y/recommended",
//     "plugin:@typescript-eslint/eslint-recommended",
//     "plugin:@typescript-eslint/recommended",
//     "plugin:@next/next/recommended",
//     "plugin:prettier/recommended",
//     "plugin:@tanstack/eslint-plugin-query/recommended",
//     "prettier"
//   ],
//   "settings": {
//     "react": {
//       "version": "detect" // React version. "detect" automatically picks the version you have installed.
//     },
//     "import/extensions": [".js", ".jsx", ".ts", ".tsx"],
//     "import/parsers": {
//       "@typescript-eslint/parser": [".ts", ".tsx"]
//     },
//     "import/resolver": {
//       "node": {
//         "extensions": [".js", ".jsx", ".ts", ".tsx"]
//       },
//       "typescript": {
//         "project": ["./tsconfig.json"]
//       }
//     }
//   },
//   "rules": {
//     "import/extensions": [
//       "error",
//       {
//         "styled": "always",
//         "graphql": "always",
//         "messages": "always",
//         "js": "never",
//         "jsx": "never",
//         "ts": "never",
//         "tsx": "never"
//       }
//     ],
//     "import/no-unresolved": [2, { "ignore": ["^@"] }],
//     "prettier/prettier": "error",
//     "no-unused-vars": "off",
//     "no-useless-constructor": "off",
//     "no-shadow": "off",
//     "no-use-before-define": "off",
//     "import/prefer-default-export": "off",
//     "no-param-reassign": [
//       "error",
//       { "props": true, "ignorePropertyModificationsFor": ["self"] }
//     ],
//     "jsx-a11y/anchor-is-valid": [
//       "error",
//       {
//         "components": ["Link"],
//         "specialLink": ["onClick", "to"]
//       }
//     ],
//     "@next/next/no-css-tags": "off",
//     "@typescript-eslint/no-explicit-any": "off",
//     "@typescript-eslint/no-useless-constructor": "error",
//     "@typescript-eslint/no-use-before-define": "off",
//     "@typescript-eslint/explicit-function-return-type": "off",
//     "@typescript-eslint/explicit-module-boundary-types": "off",
//     "@typescript-eslint/no-unused-vars": [
//       "warn",
//       { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }
//     ],
//     "@typescript-eslint/member-delimiter-style": [
//       2,
//       {
//         "multiline": {
//           "delimiter": "none",
//           "requireLast": false
//         },
//         "singleline": {
//           "delimiter": "semi",
//           "requireLast": false
//         }
//       }
//     ],
//     "@typescript-eslint/no-duplicate-enum-values": "error",
//     "react/no-danger": "off",
//     "react/require-default-props": "off",
//     "react/prop-types": "off",
//     "react/jsx-props-no-spreading": "off",
//     "react/jsx-filename-extension": [1, { "extensions": [".tsx"] }],
//     "react/react-in-jsx-scope": "off",
//     "import/order": [
//       "error",
//       {
//         "groups": [
//           "builtin",
//           "external",
//           "internal",
//           "parent",
//           "sibling",
//           "index"
//         ],
//         "newlines-between": "always"
//       }
//     ],
//     "import/newline-after-import": "error",
//     "linebreak-style": "off"
//   }
// }
{
  "root": true,
  "env": {
    "browser": true,
    "node": true,
    "es6": true
  },
  "parser": "@typescript-eslint/parser",
  "plugins": [
    "@typescript-eslint",
    "prettier",
    "simple-import-sort",
    "import",
    "jsx-a11y"
  ],
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 2018,
    "sourceType": "module"
  },
  "extends": [
    "next/core-web-vitals",
    "plugin:import/errors",
    "plugin:import/warnings",
    "plugin:import/typescript",
    "plugin:react/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:@next/next/recommended",
    "plugin:prettier/recommended",
    "plugin:@tanstack/eslint-plugin-query/recommended",
    "prettier"
  ],
  "settings": {
    "react": {
      "version": "detect"
    },
    "import/extensions": [".js", ".jsx", ".ts", ".tsx"],
    "import/parsers": {
      "@typescript-eslint/parser": [".ts", ".tsx"]
    },
    "import/resolver": {
      "node": {
        "extensions": [".js", ".jsx", ".ts", ".tsx"]
      },
      "typescript": {
        "project": ["./tsconfig.json"]
      }
    }
  },
  "rules": {
    "import/extensions": [
      "error",
      {
        "styled": "always",
        "graphql": "always",
        "messages": "always",
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never",
        "png": "always",
        "jpg": "always",
        "jpeg": "always",
        "svg": "always",
        "webp": "always"
      }
    ],
    "import/no-unresolved": [
      2,
      { "ignore": ["^@", "^/.*\\.(png|jpg|jpeg|svg|webp)$"] }
    ],
    "prettier/prettier": "error",
    "no-unused-vars": "off",
    "no-useless-constructor": "off",
    "no-shadow": "off",
    "no-use-before-define": "off",
    "import/prefer-default-export": "off",
    "no-param-reassign": [
      "error",
      { "props": true, "ignorePropertyModificationsFor": ["self"] }
    ],
    "jsx-a11y/anchor-is-valid": [
      "error",
      {
        "components": ["Link"],
        "specialLink": ["onClick", "to"]
      }
    ],
    "jsx-a11y/no-noninteractive-element-interactions": "off",
    "jsx-a11y/no-noninteractive-tabindex": "off",
    "react-hooks/exhaustive-deps": "warn",
    "@next/next/no-css-tags": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-useless-constructor": "error",
    "@typescript-eslint/no-use-before-define": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-unused-vars": [
      "warn",
      { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }
    ],
    "react/no-unescaped-entities": "off",
    "@typescript-eslint/member-delimiter-style": "off",
    "@typescript-eslint/no-duplicate-enum-values": "error",
    "react/no-danger": "off",
    "react/require-default-props": "off",
    "react/prop-types": "off",
    "react/jsx-props-no-spreading": "off",
    "react/jsx-filename-extension": [1, { "extensions": [".tsx"] }],
    "react/react-in-jsx-scope": "off",
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "newlines-between": "always"
      }
    ],
    "import/newline-after-import": "error",
    "linebreak-style": "off"
  }
}
